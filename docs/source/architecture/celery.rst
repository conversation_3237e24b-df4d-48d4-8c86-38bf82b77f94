Celery Task Processing
======================

TurdParty uses Celery for asynchronous task processing, enabling long-running operations to be executed in the background without blocking the main API.

Architecture Overview
---------------------

The Celery integration consists of:

- **Redis Broker**: Message queue for task distribution
- **Multiple Workers**: Specialized workers for different task types
- **Result Backend**: Redis-based storage for task results
- **Flower Dashboard**: Web interface for monitoring

.. code-block:: text

    ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
    │  FastAPI    │────▶│    Redis    │────▶│   Celery    │
    │ Application │     │   Broker    │     │  Workers    │
    └─────────────┘     └─────────────┘     └─────────────┘
                              │                     │
                              │                     │
                              ▼                     ▼
                        ┌─────────────┐     ┌─────────────┐
                        │   Result    │     │   Flower    │
                        │  Backend    │     │ Dashboard   │
                        │  (Redis)    │     │  (:3450)    │
                        └─────────────┘     └─────────────┘

Worker Types
------------

TurdParty runs multiple specialized Celery workers:

Default Worker
~~~~~~~~~~~~~~

- **Queue**: ``default``
- **Purpose**: General background tasks
- **Container**: ``turdparty_celery_default``
- **Status**: ✅ Healthy

File Operations Worker
~~~~~~~~~~~~~~~~~~~~~~

- **Queue**: ``file_ops``
- **Purpose**: File processing, uploads, and analysis
- **Container**: ``turdparty_celery_file_ops``
- **Status**: ⚠️ Running (health check issues)

VM Operations Worker
~~~~~~~~~~~~~~~~~~~~

- **Queue**: ``vm_ops``
- **Purpose**: VM management and file injection tasks
- **Container**: ``turdparty_celery_vm_ops``
- **Status**: ⚠️ Running (health check issues)

Task Queues
-----------

The system uses the following task queues:

1. **default**: General tasks and system operations
2. **file_ops**: File processing and analysis tasks
3. **vm_ops**: VM management and file injection tasks
4. **analysis**: Malware analysis and report generation
5. **monitoring**: Health checks and maintenance tasks

Task Examples
-------------

File Upload Processing
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from api.celery_app import celery_app
   
   @celery_app.task(queue='file_ops')
   def process_file_upload(file_id: str):
       """Process uploaded file in background."""
       # File processing logic
       return {"status": "completed", "file_id": file_id}

VM File Injection
~~~~~~~~~~~~~~~~~

.. code-block:: python

   @celery_app.task(queue='vm_ops')
   def inject_file_to_vm(vm_id: str, file_path: str):
       """Inject file into VM via SSH."""
       # VM injection logic
       return {"status": "injected", "vm_id": vm_id}

Monitoring and Management
-------------------------

Flower Dashboard
~~~~~~~~~~~~~~~~

Access the Celery monitoring dashboard at http://localhost:3450

Features:
- Real-time worker status
- Task queue monitoring
- Task history and results
- Worker performance metrics

.. note::
   The Flower dashboard requires authentication. Set the ``FLOWER_UNAUTHENTICATED_API`` 
   environment variable to enable unauthenticated access for development.

Health Checks
~~~~~~~~~~~~~

Monitor Celery health through the API:

.. code-block:: bash

   # Check Celery worker status
   curl http://localhost:3050/api/v1/celery/health
   
   # Get active tasks
   curl http://localhost:3050/api/v1/celery/active
   
   # Get worker statistics
   curl http://localhost:3050/api/v1/celery/stats

Configuration
-------------

Celery is configured with the following settings:

- **Broker URL**: ``redis://redis:6379/0``
- **Result Backend**: ``redis://redis:6379/0``
- **Task Serializer**: JSON
- **Result Serializer**: JSON
- **Timezone**: UTC

Docker Configuration
--------------------

The Celery workers are defined in the Docker Compose configuration:

.. code-block:: yaml

   celery_default:
     build:
       context: .
       dockerfile: .dockerwrapper/Dockerfile.celery
     command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "default", "-c", "2"]
     depends_on:
       - redis
       - api

   celery_file_ops:
     build:
       context: .
       dockerfile: .dockerwrapper/Dockerfile.celery
     command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "file_ops", "-c", "2"]

   celery_vm_ops:
     build:
       context: .
       dockerfile: .dockerwrapper/Dockerfile.celery
     command: ["celery", "-A", "api.celery_app", "worker", "--loglevel=info", "-Q", "vm_ops", "-c", "2"]

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

1. **Workers showing as unhealthy**: Check health check configuration
2. **Tasks not processing**: Verify Redis connection and worker status
3. **Flower authentication errors**: Set ``FLOWER_UNAUTHENTICATED_API=true``

Debugging
~~~~~~~~~

.. code-block:: bash

   # Check worker logs
   docker logs turdparty_celery_default
   docker logs turdparty_celery_file_ops
   docker logs turdparty_celery_vm_ops
   
   # Check Redis connectivity
   docker exec turdparty_redis redis-cli ping
   
   # Restart workers
   docker restart turdparty_celery_default

For more information on Celery configuration and usage, see the :doc:`../api/index` section.
