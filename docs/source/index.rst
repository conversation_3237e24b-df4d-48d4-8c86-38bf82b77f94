Welcome to TurdParty Documentation
===============================

.. image:: _static/logo.png
   :alt: TurdParty Logo
   :align: center
   :width: 200px

TurdParty is a comprehensive microservices application for managing Vagrant virtual machines, file uploads, MinIO storage, and service monitoring with integrated status dashboards.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   getting-started/index
   architecture/index
   api/index
   development/index
   deployment/index
   troubleshooting/index
   user-guides/index
   reference/index

Features
--------

- **Vagrant VM Management**: Create, manage, and monitor Vagrant virtual machines
- **File Upload & Storage**: Upload and manage files with MinIO object storage
- **VM Injection**: Inject files into Vagrant VMs via SSH or gRPC
- **Static Analysis**: Analyze files for security and quality
- **Service Monitoring**: Real-time service status with Cachet dashboard
- **Async Task Processing**: Background task processing with Celery workers
- **API Integration**: Comprehensive REST API for automation and integration
- **Container Orchestration**: Docker-based microservices architecture

About This Documentation
-----------------------

This documentation is built using Sphinx and includes:

- Installation and configuration guides
- Architecture documentation
- API reference
- Development guides
- Troubleshooting information
- User guides
- Reference materials

The documentation is available in HTML format for online viewing and PDF format for offline reference.

Getting Started
--------------

Check out the :doc:`getting-started/index` section for information on how to install and use TurdParty.

Development
----------

For developers, the :doc:`development/index` section provides information on contributing to the project,
testing procedures, and the development roadmap.

Architecture
-----------

The :doc:`architecture/index` section provides detailed information about the system architecture,
including Vagrant VM management, MinIO storage, and the FastAPI application infrastructure.

Indices and Tables
-----------------

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
